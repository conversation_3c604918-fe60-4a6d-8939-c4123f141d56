import React from 'react';
import {
  Modal as RNModal,
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { X } from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from './Button';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export type ModalSize = 'sm' | 'md' | 'lg' | 'xl' | 'fullscreen';

interface ModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: ModalSize;
  showCloseButton?: boolean;
  closeOnBackdrop?: boolean;
  style?: ViewStyle;
  gradient?: boolean;
  scrollable?: boolean;
}

export const Modal: React.FC<ModalProps> = ({
  visible,
  onClose,
  title,
  children,
  size = 'md',
  showCloseButton = true,
  closeOnBackdrop = true,
  style,
  gradient = false,
  scrollable = true,
}) => {
  const { theme } = useTheme();
  const scale = useSharedValue(0.9);
  const opacity = useSharedValue(0);

  React.useEffect(() => {
    if (visible) {
      opacity.value = withTiming(1, { duration: 200 });
      scale.value = withSpring(1, { damping: 15, stiffness: 300 });
    } else {
      opacity.value = withTiming(0, { duration: 150 });
      scale.value = withTiming(0.9, { duration: 150 });
    }
  }, [visible]);

  const animatedOverlayStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  const animatedModalStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const getSizeStyles = (): ViewStyle => {
    const sizeStyles: Record<ModalSize, ViewStyle> = {
      sm: {
        width: Math.min(screenWidth * 0.8, 320),
        maxHeight: screenHeight * 0.6,
      },
      md: {
        width: Math.min(screenWidth * 0.85, 400),
        maxHeight: screenHeight * 0.7,
      },
      lg: {
        width: Math.min(screenWidth * 0.9, 500),
        maxHeight: screenHeight * 0.8,
      },
      xl: {
        width: Math.min(screenWidth * 0.95, 600),
        maxHeight: screenHeight * 0.85,
      },
      fullscreen: {
        width: screenWidth,
        height: screenHeight,
        borderRadius: 0,
      },
    };
    return sizeStyles[size];
  };

  const handleBackdropPress = () => {
    if (closeOnBackdrop) {
      runOnJS(onClose)();
    }
  };

  const modalContent = (
    <View style={[styles.modalContent, getSizeStyles(), style]}>
      {/* Header */}
      {(title || showCloseButton) && (
        <View style={[styles.modalHeader, { borderBottomColor: theme.colors.ui.border }]}>
          {title && (
            <Text style={[styles.modalTitle, { color: theme.colors.text.primary }]}>
              {title}
            </Text>
          )}
          {showCloseButton && (
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <X size={24} color={theme.colors.text.secondary} />
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* Body */}
      <View style={styles.modalBody}>
        {scrollable ? (
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {children}
          </ScrollView>
        ) : (
          children
        )}
      </View>
    </View>
  );

  return (
    <RNModal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
    >
      <Animated.View style={[styles.overlay, animatedOverlayStyle]}>
        <TouchableOpacity
          style={styles.backdrop}
          activeOpacity={1}
          onPress={handleBackdropPress}
        >
          <Animated.View style={[animatedModalStyle]}>
            <TouchableOpacity activeOpacity={1} onPress={() => {}}>
              {gradient ? (
                <LinearGradient
                  colors={theme.colors.gradients.primary as readonly [string, string]}
                  style={[styles.modalContent, getSizeStyles(), style]}
                >
                  {modalContent}
                </LinearGradient>
              ) : (
                <View style={[{ backgroundColor: theme.colors.background.modal }]}>
                  {modalContent}
                </View>
              )}
            </TouchableOpacity>
          </Animated.View>
        </TouchableOpacity>
      </Animated.View>
    </RNModal>
  );
};

// Confirmation Modal variant
interface ConfirmationModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'danger';
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  visible,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
}) => {
  const { theme } = useTheme();

  return (
    <Modal
      visible={visible}
      onClose={onClose}
      title={title}
      size="sm"
      scrollable={false}
    >
      <View style={styles.confirmationContent}>
        <Text style={[styles.confirmationMessage, { color: theme.colors.text.secondary }]}>
          {message}
        </Text>
        
        <View style={styles.confirmationActions}>
          <Button
            title={cancelText}
            onPress={onClose}
            variant="outline"
            style={styles.confirmationButton}
          />
          <Button
            title={confirmText}
            onPress={() => {
              onConfirm();
              onClose();
            }}
            variant={variant === 'danger' ? 'danger' : 'primary'}
            style={styles.confirmationButton}
          />
        </View>
      </View>
    </Modal>
  );
};

// Bottom Sheet Modal variant
interface BottomSheetModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  height?: number;
  showCloseButton?: boolean;
}

export const BottomSheetModal: React.FC<BottomSheetModalProps> = ({
  visible,
  onClose,
  title,
  children,
  height = screenHeight * 0.6,
  showCloseButton = true,
}) => {
  const { theme } = useTheme();
  const translateY = useSharedValue(height);
  const opacity = useSharedValue(0);

  React.useEffect(() => {
    if (visible) {
      opacity.value = withTiming(1, { duration: 200 });
      translateY.value = withSpring(0, { damping: 15, stiffness: 300 });
    } else {
      opacity.value = withTiming(0, { duration: 150 });
      translateY.value = withTiming(height, { duration: 200 });
    }
  }, [visible, height]);

  const animatedOverlayStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  const animatedSheetStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  return (
    <RNModal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
    >
      <Animated.View style={[styles.overlay, animatedOverlayStyle]}>
        <TouchableOpacity
          style={styles.backdrop}
          activeOpacity={1}
          onPress={onClose}
        >
          <Animated.View style={[styles.bottomSheet, { height }, animatedSheetStyle]}>
            <TouchableOpacity activeOpacity={1} onPress={() => {}}>
              <View style={[styles.bottomSheetContent, { backgroundColor: theme.colors.background.modal }]}>
                {/* Handle */}
                <View style={[styles.bottomSheetHandle, { backgroundColor: theme.colors.ui.border }]} />
                
                {/* Header */}
                {(title || showCloseButton) && (
                  <View style={[styles.bottomSheetHeader, { borderBottomColor: theme.colors.ui.border }]}>
                    {title && (
                      <Text style={[styles.modalTitle, { color: theme.colors.text.primary }]}>
                        {title}
                      </Text>
                    )}
                    {showCloseButton && (
                      <TouchableOpacity
                        style={styles.closeButton}
                        onPress={onClose}
                        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                      >
                        <X size={24} color={theme.colors.text.secondary} />
                      </TouchableOpacity>
                    )}
                  </View>
                )}

                {/* Body */}
                <ScrollView
                  style={styles.bottomSheetBody}
                  showsVerticalScrollIndicator={false}
                  contentContainerStyle={styles.scrollContent}
                >
                  {children}
                </ScrollView>
              </View>
            </TouchableOpacity>
          </Animated.View>
        </TouchableOpacity>
      </Animated.View>
    </RNModal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backdrop: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  modalBody: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  confirmationContent: {
    padding: 20,
  },
  confirmationMessage: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    lineHeight: 24,
    marginBottom: 24,
    textAlign: 'center',
  },
  confirmationActions: {
    flexDirection: 'row',
    gap: 12,
  },
  confirmationButton: {
    flex: 1,
  },
  bottomSheet: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  bottomSheetContent: {
    flex: 1,
  },
  bottomSheetHandle: {
    width: 40,
    height: 4,
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 8,
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
  },
  bottomSheetBody: {
    flex: 1,
  },
});
