import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { useAnalytics } from '@/hooks/useAnalytics';
import { useTheme } from '@/contexts/ThemeContext';
import { AnalyticsPeriod } from '@/types/app';

// Tab Components
import { OverviewTab } from './tabs/OverviewTab';
import { DailyTab } from './tabs/DailyTab';
import { WeeklyTab } from './tabs/WeeklyTab';
import { MonthlyTab } from './tabs/MonthlyTab';
import { SubjectsTab } from './tabs/SubjectsTab';
import { TaskTypesTab } from './tabs/TaskTypesTab';

const { width } = Dimensions.get('window');

interface AnalyticsTab {
  id: string;
  title: string;
  icon: keyof typeof MaterialIcons.glyphMap;
}

const ANALYTICS_TABS: AnalyticsTab[] = [
  { id: 'overview', title: 'Overview', icon: 'dashboard' },
  { id: 'daily', title: 'Daily', icon: 'today' },
  { id: 'weekly', title: 'Weekly', icon: 'view-week' },
  { id: 'monthly', title: 'Monthly', icon: 'calendar-month' },
  { id: 'subjects', title: 'Subjects', icon: 'school' },
  { id: 'task-types', title: 'Task Types', icon: 'category' },
];

const PERIOD_OPTIONS: { value: AnalyticsPeriod; label: string }[] = [
  { value: 'week', label: 'Week' },
  { value: 'month', label: 'Month' },
  { value: 'year', label: 'Year' },
  { value: 'all', label: 'All Time' },
];

export function AnalyticsDashboard() {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState('overview');
  const {
    loading,
    error,
    selectedPeriod,
    changePeriod,
    refreshAnalytics,
    quickStats,
    analytics,
    timeSeriesData,
    subjectChartData,
    taskTypeChartData,
    performanceMetrics,
    todayProgress,
    weekProgress,
    formatTime,
    getStreakMessage,
  } = useAnalytics();

  const renderTabContent = () => {
    const commonProps = {
      analytics,
      timeSeriesData,
      subjectChartData,
      taskTypeChartData,
      performanceMetrics,
      quickStats,
      todayProgress,
      weekProgress,
      formatTime,
      getStreakMessage,
      loading,
      error,
    };

    switch (activeTab) {
      case 'overview':
        return <OverviewTab {...commonProps} />;
      case 'daily':
        return <DailyTab {...commonProps} />;
      case 'weekly':
        return <WeeklyTab {...commonProps} />;
      case 'monthly':
        return <MonthlyTab {...commonProps} />;
      case 'subjects':
        return <SubjectsTab {...commonProps} />;
      case 'task-types':
        return <TaskTypesTab {...commonProps} />;
      default:
        return <OverviewTab {...commonProps} />;
    }
  };

  const renderPeriodSelector = () => {
    if (activeTab === 'daily' || activeTab === 'overview') {
      // These tabs have their own period controls
      return null;
    }

    return (
      <View style={[styles.periodSelector, {
        backgroundColor: theme.colors.background.card,
        borderBottomColor: theme.colors.ui.border,
      }]}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {PERIOD_OPTIONS.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.periodButton,
                { backgroundColor: theme.colors.background.secondary },
                selectedPeriod === option.value && [
                  styles.periodButtonActive,
                  { backgroundColor: theme.colors.accent.primary }
                ],
              ]}
              onPress={() => changePeriod(option.value)}
            >
              <Text
                style={[
                  styles.periodText,
                  { color: theme.colors.text.secondary },
                  selectedPeriod === option.value && [
                    styles.periodTextActive,
                    { color: theme.colors.text.inverse }
                  ],
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background.primary }]}>
      {/* Header */}
      <LinearGradient
        colors={theme.colors.gradients.primary}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Analytics</Text>
          <Text style={styles.headerSubtitle}>Track your study progress</Text>
        </View>

        {/* Tab Navigation */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.tabScrollView}
          contentContainerStyle={styles.tabContainer}
        >
          {ANALYTICS_TABS.map((tab) => (
            <TouchableOpacity
              key={tab.id}
              style={[
                styles.tab,
                activeTab === tab.id && styles.tabActive,
              ]}
              onPress={() => setActiveTab(tab.id)}
            >
              <MaterialIcons
                name={tab.icon}
                size={20}
                color={activeTab === tab.id ? theme.colors.text.inverse : theme.colors.text.inverse + '80'}
              />
              <Text
                style={[
                  styles.tabText,
                  activeTab === tab.id && styles.tabTextActive,
                  { color: activeTab === tab.id ? theme.colors.text.inverse : theme.colors.text.inverse + '80' }
                ]}
              >
                {tab.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </LinearGradient>

      {/* Period Selector */}
      {renderPeriodSelector()}

      {/* Tab Content */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={refreshAnalytics}
            colors={[theme.colors.accent.primary]}
            tintColor={theme.colors.accent.primary}
          />
        }
      >
        {renderTabContent()}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.8,
  },
  tabScrollView: {
    marginHorizontal: -20,
  },
  tabContainer: {
    paddingHorizontal: 20,
    gap: 8,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    gap: 6,
  },
  tabActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  tabTextActive: {
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  periodSelector: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    marginRight: 8,
  },
  periodButtonActive: {
    // Active styles will be applied inline with theme colors
  },
  periodText: {
    fontSize: 14,
    fontWeight: '500',
  },
  periodTextActive: {
    fontWeight: '600',
  },
});
