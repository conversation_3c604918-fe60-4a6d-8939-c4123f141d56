import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { Analytics, ChartDataPoint } from '@/types/app';

const { width } = Dimensions.get('window');

interface SubjectsTabProps {
  analytics: Analytics | null;
  subjectChartData: ChartDataPoint[];
  formatTime: (seconds: number) => string;
  loading: boolean;
  error: string | null;
}

export function SubjectsTab({ 
  analytics, 
  subjectChartData, 
  formatTime, 
  loading, 
  error 
}: SubjectsTabProps) {
  
  const renderSubjectChart = () => {
    if (!analytics?.subjectStats || analytics.subjectStats.length === 0) return null;

    const maxDuration = Math.max(...analytics.subjectStats.map(stat => stat.totalDuration));
    const chartHeight = 120;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Subject Study Time</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.barChart}>
            {analytics.subjectStats.map((stat, index) => {
              const height = maxDuration > 0 ? (stat.totalDuration / maxDuration) * chartHeight : 0;
              
              return (
                <View key={stat.subject} style={styles.barContainer}>
                  <View style={[styles.barWrapper, { height: chartHeight }]}>
                    <View
                      style={[
                        styles.bar,
                        { 
                          height: Math.max(height, 4),
                          backgroundColor: stat.color
                        }
                      ]}
                    />
                  </View>
                  <Text style={styles.barLabel} numberOfLines={2}>
                    {stat.subject}
                  </Text>
                  <Text style={styles.barValue}>
                    {formatTime(stat.totalDuration)}
                  </Text>
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>
    );
  };

  const renderPieChart = () => {
    if (!subjectChartData || subjectChartData.length === 0) return null;

    const totalTime = subjectChartData.reduce((sum, item) => sum + item.value, 0);

    return (
      <View style={styles.pieContainer}>
        <Text style={styles.pieTitle}>Subject Distribution</Text>
        
        {/* Pie chart representation */}
        <View style={styles.pieChart}>
          {subjectChartData.map((item, index) => (
            <View
              key={item.label}
              style={[
                styles.pieSlice,
                {
                  backgroundColor: item.color,
                  width: `${item.percentage || 0}%`,
                }
              ]}
            />
          ))}
        </View>

        {/* Legend */}
        <View style={styles.pieLegend}>
          {subjectChartData.map((item, index) => (
            <View key={item.label} style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: item.color }]} />
              <Text style={styles.legendLabel}>{item.label}</Text>
              <Text style={styles.legendValue}>
                {(item.percentage || 0).toFixed(1)}%
              </Text>
              <Text style={styles.legendTime}>
                ({formatTime(item.value * 60)})
              </Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderSubjectList = () => {
    if (!analytics?.subjectStats || analytics.subjectStats.length === 0) return null;

    return (
      <View style={styles.listContainer}>
        <Text style={styles.listTitle}>Subject Details</Text>
        {analytics.subjectStats.map((stat, index) => {
          const averageSession = stat.sessionCount > 0 ? stat.totalDuration / stat.sessionCount : 0;
          
          return (
            <View key={stat.subject} style={styles.subjectItem}>
              <View style={styles.subjectHeader}>
                <View style={styles.subjectInfo}>
                  <View style={[styles.subjectColor, { backgroundColor: stat.color }]} />
                  <View style={styles.subjectDetails}>
                    <Text style={styles.subjectName}>{stat.subject}</Text>
                    <Text style={styles.subjectRank}>#{index + 1} most studied</Text>
                  </View>
                </View>
                <Text style={styles.subjectDuration}>
                  {formatTime(stat.totalDuration)}
                </Text>
              </View>
              
              <View style={styles.subjectStats}>
                <View style={styles.subjectStatItem}>
                  <MaterialIcons name="play-circle-outline" size={16} color="#64748B" />
                  <Text style={styles.subjectStatText}>
                    {stat.sessionCount} session{stat.sessionCount !== 1 ? 's' : ''}
                  </Text>
                </View>
                
                <View style={styles.subjectStatItem}>
                  <MaterialIcons name="trending-up" size={16} color="#64748B" />
                  <Text style={styles.subjectStatText}>
                    {formatTime(averageSession)} avg
                  </Text>
                </View>
                
                {stat.averageProductivityRating > 0 && (
                  <View style={styles.subjectStatItem}>
                    <MaterialIcons name="star" size={16} color="#F59E0B" />
                    <Text style={styles.subjectStatText}>
                      {stat.averageProductivityRating.toFixed(1)} rating
                    </Text>
                  </View>
                )}
              </View>

              {/* Progress bar showing relative time */}
              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      { 
                        backgroundColor: stat.color,
                        width: `${stat.percentage}%`
                      }
                    ]}
                  />
                </View>
                <Text style={styles.progressText}>
                  {(stat.percentage || 0).toFixed(1)}% of total study time
                </Text>
              </View>
            </View>
          );
        })}
      </View>
    );
  };

  const renderSubjectSummary = () => {
    if (!analytics?.subjectStats || analytics.subjectStats.length === 0) return null;

    const totalSubjects = analytics.subjectStats.length;
    const totalTime = analytics.subjectStats.reduce((sum, stat) => sum + stat.totalDuration, 0);
    const totalSessions = analytics.subjectStats.reduce((sum, stat) => sum + stat.sessionCount, 0);
    const topSubject = analytics.subjectStats[0];
    const averageTimePerSubject = totalSubjects > 0 ? totalTime / totalSubjects : 0;

    return (
      <View style={styles.summaryContainer}>
        <Text style={styles.summaryTitle}>Subject Summary</Text>
        <View style={styles.summaryGrid}>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{totalSubjects}</Text>
            <Text style={styles.summaryLabel}>Total Subjects</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{formatTime(averageTimePerSubject)}</Text>
            <Text style={styles.summaryLabel}>Avg/Subject</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue} numberOfLines={1}>
              {topSubject?.subject || 'None'}
            </Text>
            <Text style={styles.summaryLabel}>Top Subject</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>
              {topSubject?.percentage !== undefined ? `${topSubject.percentage.toFixed(0)}%` : '0%'}
            </Text>
            <Text style={styles.summaryLabel}>Top Share</Text>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <MaterialIcons name="school" size={48} color="#6366F1" />
        <Text style={styles.loadingText}>Loading subject analytics...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialIcons name="error-outline" size={48} color="#EF4444" />
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  if (!analytics?.subjectStats || analytics.subjectStats.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <MaterialIcons name="school" size={64} color="#94A3B8" />
        <Text style={styles.emptyTitle}>No Subject Data</Text>
        <Text style={styles.emptyText}>
          Start studying with subjects to see your subject analytics here.
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {renderSubjectSummary()}
      {renderPieChart()}
      {renderSubjectChart()}
      {renderSubjectList()}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryContainer: {
    margin: 20,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    minWidth: (width - 52) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 4,
    textAlign: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    color: '#64748B',
    textAlign: 'center',
  },
  pieContainer: {
    backgroundColor: '#FFFFFF',
    margin: 20,
    marginTop: 0,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  pieTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  pieChart: {
    flexDirection: 'row',
    height: 12,
    backgroundColor: '#E2E8F0',
    borderRadius: 6,
    overflow: 'hidden',
    marginBottom: 16,
  },
  pieSlice: {
    height: '100%',
  },
  pieLegend: {
    gap: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  legendLabel: {
    flex: 1,
    fontSize: 14,
    color: '#1E293B',
  },
  legendValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6366F1',
    minWidth: 40,
  },
  legendTime: {
    fontSize: 12,
    color: '#64748B',
  },
  chartContainer: {
    backgroundColor: '#FFFFFF',
    margin: 20,
    marginTop: 0,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  barChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
    paddingBottom: 40,
  },
  barContainer: {
    alignItems: 'center',
    minWidth: 60,
  },
  barWrapper: {
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  bar: {
    width: 32,
    borderRadius: 16,
    minHeight: 4,
  },
  barLabel: {
    fontSize: 10,
    color: '#64748B',
    marginBottom: 2,
    textAlign: 'center',
    width: 60,
  },
  barValue: {
    fontSize: 9,
    color: '#64748B',
    textAlign: 'center',
  },
  listContainer: {
    margin: 20,
    marginTop: 0,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  subjectItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  subjectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  subjectInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  subjectColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  subjectDetails: {
    flex: 1,
  },
  subjectName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 2,
  },
  subjectRank: {
    fontSize: 12,
    color: '#64748B',
  },
  subjectDuration: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6366F1',
  },
  subjectStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 12,
  },
  subjectStatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  subjectStatText: {
    fontSize: 12,
    color: '#64748B',
  },
  progressContainer: {
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
    paddingTop: 12,
  },
  progressBar: {
    height: 6,
    backgroundColor: '#E2E8F0',
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: '#64748B',
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#64748B',
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    marginTop: 12,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#64748B',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#94A3B8',
    textAlign: 'center',
    lineHeight: 24,
  },
});
