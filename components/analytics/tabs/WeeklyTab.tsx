import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { Analytics, WeeklyStat } from '@/types/app';
import { useTheme } from '@/contexts/ThemeContext';

const { width } = Dimensions.get('window');

interface WeeklyTabProps {
  analytics: Analytics | null;
  formatTime: (seconds: number) => string;
  loading: boolean;
  error: string | null;
}

export function WeeklyTab({ analytics, formatTime, loading, error }: WeeklyTabProps) {
  const { theme } = useTheme();

  const renderWeeklyChart = () => {
    if (!analytics?.weeklyStats || analytics.weeklyStats.length === 0) return null;

    const maxDuration = Math.max(...analytics.weeklyStats.map(stat => stat.totalDuration));
    const chartHeight = 120;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Weekly Study Time</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.barChart}>
            {analytics.weeklyStats.map((stat, index) => {
              const height = maxDuration > 0 ? (stat.totalDuration / maxDuration) * chartHeight : 0;
              const weekStart = new Date(stat.weekStart);
              const weekEnd = new Date(stat.weekEnd);
              const isCurrentWeek = weekEnd >= new Date();
              
              return (
                <View key={`${stat.weekStart}-${stat.weekEnd}`} style={styles.barContainer}>
                  <View style={[styles.barWrapper, { height: chartHeight }]}>
                    <LinearGradient
                      colors={stat.targetAchieved ? ['#10B981', '#059669'] : ['#6366F1', '#8B5CF6']}
                      style={[
                        styles.bar,
                        { 
                          height: Math.max(height, 4),
                          opacity: isCurrentWeek ? 1 : 0.8
                        }
                      ]}
                    />
                  </View>
                  <Text style={[styles(theme).barLabel, isCurrentWeek && styles(theme).barLabelCurrent]}>
                    {weekStart.toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric'
                    })}
                  </Text>
                  <Text style={styles(theme).barValue}>
                    {formatTime(stat.totalDuration)}
                  </Text>
                  {stat.targetAchieved && (
                    <MaterialIcons name="check-circle" size={12} color="#10B981" />
                  )}
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>
    );
  };

  const renderWeeklyList = () => {
    if (!analytics?.weeklyStats || analytics.weeklyStats.length === 0) return null;

    return (
      <View style={styles.listContainer}>
        <Text style={styles.listTitle}>Weekly Breakdown</Text>
        {analytics.weeklyStats.map((stat, index) => {
          const weekStart = new Date(stat.weekStart);
          const weekEnd = new Date(stat.weekEnd);
          const isCurrentWeek = weekEnd >= new Date();
          
          const weekLabel = isCurrentWeek ? 'This Week' : 
            `${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`;

          return (
            <View key={`${stat.weekStart}-${stat.weekEnd}`} style={styles.weeklyItem}>
              <View style={styles.weeklyHeader}>
                <View style={styles.weeklyDateContainer}>
                  <Text style={[styles.weeklyDate, isCurrentWeek && styles.weeklyDateCurrent]}>
                    {weekLabel}
                  </Text>
                  {stat.targetAchieved && (
                    <MaterialIcons name="check-circle" size={16} color="#10B981" />
                  )}
                </View>
                <Text style={styles.weeklyDuration}>
                  {formatTime(stat.totalDuration)}
                </Text>
              </View>
              
              <View style={styles.weeklyDetails}>
                <View style={styles.weeklyDetailItem}>
                  <MaterialIcons name="play-circle-outline" size={16} color="#64748B" />
                  <Text style={styles.weeklyDetailText}>
                    {stat.sessionCount} session{stat.sessionCount !== 1 ? 's' : ''}
                  </Text>
                </View>
                
                <View style={styles.weeklyDetailItem}>
                  <MaterialIcons name="calendar-today" size={16} color="#64748B" />
                  <Text style={styles.weeklyDetailText}>
                    {stat.activeDays} active day{stat.activeDays !== 1 ? 's' : ''}
                  </Text>
                </View>
                
                {stat.completedPomodoros > 0 && (
                  <View style={styles.weeklyDetailItem}>
                    <MaterialIcons name="timer" size={16} color="#64748B" />
                    <Text style={styles.weeklyDetailText}>
                      {stat.completedPomodoros} pomodoro{stat.completedPomodoros !== 1 ? 's' : ''}
                    </Text>
                  </View>
                )}
                
                {stat.averageProductivityRating > 0 && (
                  <View style={styles.weeklyDetailItem}>
                    <MaterialIcons name="star" size={16} color="#F59E0B" />
                    <Text style={styles.weeklyDetailText}>
                      {stat.averageProductivityRating.toFixed(1)} avg rating
                    </Text>
                  </View>
                )}
              </View>

              {/* Progress bar for the week */}
              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <LinearGradient
                    colors={['#6366F1', '#8B5CF6']}
                    style={[
                      styles.progressFill,
                      { width: `${Math.min((stat.totalDuration / (7 * 60 * 60)) * 100, 100)}%` }
                    ]}
                  />
                </View>
                <Text style={styles.progressText}>
                  {Math.round((stat.totalDuration / (7 * 60 * 60)) * 100)}% of 7h goal
                </Text>
              </View>
            </View>
          );
        })}
      </View>
    );
  };

  const renderWeeklySummary = () => {
    if (!analytics?.weeklyStats || analytics.weeklyStats.length === 0) return null;

    const totalTime = analytics.weeklyStats.reduce((sum, stat) => sum + stat.totalDuration, 0);
    const totalSessions = analytics.weeklyStats.reduce((sum, stat) => sum + stat.sessionCount, 0);
    const totalActiveDays = analytics.weeklyStats.reduce((sum, stat) => sum + stat.activeDays, 0);
    const weeksWithTarget = analytics.weeklyStats.filter(stat => stat.targetAchieved).length;
    const averageWeeklyTime = analytics.weeklyStats.length > 0 ? totalTime / analytics.weeklyStats.length : 0;

    return (
      <View style={styles.summaryContainer}>
        <Text style={styles.summaryTitle}>Weekly Summary</Text>
        <View style={styles.summaryGrid}>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{formatTime(totalTime)}</Text>
            <Text style={styles.summaryLabel}>Total Time</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{formatTime(averageWeeklyTime)}</Text>
            <Text style={styles.summaryLabel}>Avg/Week</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{totalSessions}</Text>
            <Text style={styles.summaryLabel}>Total Sessions</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{weeksWithTarget}</Text>
            <Text style={styles.summaryLabel}>Goals Met</Text>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <MaterialIcons name="view-week" size={48} color="#6366F1" />
        <Text style={styles.loadingText}>Loading weekly analytics...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialIcons name="error-outline" size={48} color="#EF4444" />
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {renderWeeklySummary()}
      {renderWeeklyChart()}
      {renderWeeklyList()}
    </ScrollView>
  );
}

const styles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryContainer: {
    margin: 20,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    minWidth: (width - 52) / 2,
    backgroundColor: theme.colors.background.card,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    ...theme.shadows.sm,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    color: theme.colors.text.secondary,
  },
  chartContainer: {
    backgroundColor: theme.colors.background.card,
    margin: 20,
    marginTop: 0,
    borderRadius: 16,
    padding: 20,
    ...theme.shadows.md,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 16,
  },
  barChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
    paddingBottom: 40,
  },
  barContainer: {
    alignItems: 'center',
    minWidth: 50,
  },
  barWrapper: {
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  bar: {
    width: 32,
    borderRadius: 16,
    minHeight: 4,
  },
  barLabel: {
    fontSize: 10,
    color: theme.colors.text.secondary,
    marginBottom: 2,
    textAlign: 'center',
  },
  barLabelCurrent: {
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  barValue: {
    fontSize: 9,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  listContainer: {
    margin: 20,
    marginTop: 0,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 16,
  },
  weeklyItem: {
    backgroundColor: theme.colors.background.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    ...theme.shadows.sm,
  },
  weeklyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  weeklyDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
  },
  weeklyDate: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  weeklyDateCurrent: {
    color: theme.colors.accent.primary,
    fontWeight: '600',
  },
  weeklyDuration: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.accent.primary,
  },
  weeklyDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 12,
  },
  weeklyDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  weeklyDetailText: {
    fontSize: 12,
    color: theme.colors.text.secondary,
  },
  progressContainer: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.ui.border,
    paddingTop: 12,
  },
  progressBar: {
    height: 6,
    backgroundColor: theme.colors.ui.progressBarTrack,
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.text.secondary,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: theme.colors.status.error,
    marginTop: 12,
    textAlign: 'center',
  },
});
