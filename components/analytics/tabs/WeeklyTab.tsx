import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { Analytics, WeeklyStat } from '@/types/app';

const { width } = Dimensions.get('window');

interface WeeklyTabProps {
  analytics: Analytics | null;
  formatTime: (seconds: number) => string;
  loading: boolean;
  error: string | null;
}

export function WeeklyTab({ analytics, formatTime, loading, error }: WeeklyTabProps) {
  
  const renderWeeklyChart = () => {
    if (!analytics?.weeklyStats || analytics.weeklyStats.length === 0) return null;

    const maxDuration = Math.max(...analytics.weeklyStats.map(stat => stat.totalDuration));
    const chartHeight = 120;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Weekly Study Time</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.barChart}>
            {analytics.weeklyStats.map((stat, index) => {
              const height = maxDuration > 0 ? (stat.totalDuration / maxDuration) * chartHeight : 0;
              const weekStart = new Date(stat.weekStart);
              const weekEnd = new Date(stat.weekEnd);
              const isCurrentWeek = weekEnd >= new Date();
              
              return (
                <View key={`${stat.weekStart}-${stat.weekEnd}`} style={styles.barContainer}>
                  <View style={[styles.barWrapper, { height: chartHeight }]}>
                    <LinearGradient
                      colors={stat.targetAchieved ? ['#10B981', '#059669'] : ['#6366F1', '#8B5CF6']}
                      style={[
                        styles.bar,
                        { 
                          height: Math.max(height, 4),
                          opacity: isCurrentWeek ? 1 : 0.8
                        }
                      ]}
                    />
                  </View>
                  <Text style={[styles.barLabel, isCurrentWeek && styles.barLabelCurrent]}>
                    {weekStart.toLocaleDateString('en-US', { 
                      month: 'short', 
                      day: 'numeric' 
                    })}
                  </Text>
                  <Text style={styles.barValue}>
                    {formatTime(stat.totalDuration)}
                  </Text>
                  {stat.targetAchieved && (
                    <MaterialIcons name="check-circle" size={12} color="#10B981" />
                  )}
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>
    );
  };

  const renderWeeklyList = () => {
    if (!analytics?.weeklyStats || analytics.weeklyStats.length === 0) return null;

    return (
      <View style={styles.listContainer}>
        <Text style={styles.listTitle}>Weekly Breakdown</Text>
        {analytics.weeklyStats.map((stat, index) => {
          const weekStart = new Date(stat.weekStart);
          const weekEnd = new Date(stat.weekEnd);
          const isCurrentWeek = weekEnd >= new Date();
          
          const weekLabel = isCurrentWeek ? 'This Week' : 
            `${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`;

          return (
            <View key={`${stat.weekStart}-${stat.weekEnd}`} style={styles.weeklyItem}>
              <View style={styles.weeklyHeader}>
                <View style={styles.weeklyDateContainer}>
                  <Text style={[styles.weeklyDate, isCurrentWeek && styles.weeklyDateCurrent]}>
                    {weekLabel}
                  </Text>
                  {stat.targetAchieved && (
                    <MaterialIcons name="check-circle" size={16} color="#10B981" />
                  )}
                </View>
                <Text style={styles.weeklyDuration}>
                  {formatTime(stat.totalDuration)}
                </Text>
              </View>
              
              <View style={styles.weeklyDetails}>
                <View style={styles.weeklyDetailItem}>
                  <MaterialIcons name="play-circle-outline" size={16} color="#64748B" />
                  <Text style={styles.weeklyDetailText}>
                    {stat.sessionCount} session{stat.sessionCount !== 1 ? 's' : ''}
                  </Text>
                </View>
                
                <View style={styles.weeklyDetailItem}>
                  <MaterialIcons name="calendar-today" size={16} color="#64748B" />
                  <Text style={styles.weeklyDetailText}>
                    {stat.activeDays} active day{stat.activeDays !== 1 ? 's' : ''}
                  </Text>
                </View>
                
                {stat.completedPomodoros > 0 && (
                  <View style={styles.weeklyDetailItem}>
                    <MaterialIcons name="timer" size={16} color="#64748B" />
                    <Text style={styles.weeklyDetailText}>
                      {stat.completedPomodoros} pomodoro{stat.completedPomodoros !== 1 ? 's' : ''}
                    </Text>
                  </View>
                )}
                
                {stat.averageProductivityRating > 0 && (
                  <View style={styles.weeklyDetailItem}>
                    <MaterialIcons name="star" size={16} color="#F59E0B" />
                    <Text style={styles.weeklyDetailText}>
                      {stat.averageProductivityRating.toFixed(1)} avg rating
                    </Text>
                  </View>
                )}
              </View>

              {/* Progress bar for the week */}
              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <LinearGradient
                    colors={['#6366F1', '#8B5CF6']}
                    style={[
                      styles.progressFill,
                      { width: `${Math.min((stat.totalDuration / (7 * 60 * 60)) * 100, 100)}%` }
                    ]}
                  />
                </View>
                <Text style={styles.progressText}>
                  {Math.round((stat.totalDuration / (7 * 60 * 60)) * 100)}% of 7h goal
                </Text>
              </View>
            </View>
          );
        })}
      </View>
    );
  };

  const renderWeeklySummary = () => {
    if (!analytics?.weeklyStats || analytics.weeklyStats.length === 0) return null;

    const totalTime = analytics.weeklyStats.reduce((sum, stat) => sum + stat.totalDuration, 0);
    const totalSessions = analytics.weeklyStats.reduce((sum, stat) => sum + stat.sessionCount, 0);
    const totalActiveDays = analytics.weeklyStats.reduce((sum, stat) => sum + stat.activeDays, 0);
    const weeksWithTarget = analytics.weeklyStats.filter(stat => stat.targetAchieved).length;
    const averageWeeklyTime = analytics.weeklyStats.length > 0 ? totalTime / analytics.weeklyStats.length : 0;

    return (
      <View style={styles.summaryContainer}>
        <Text style={styles.summaryTitle}>Weekly Summary</Text>
        <View style={styles.summaryGrid}>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{formatTime(totalTime)}</Text>
            <Text style={styles.summaryLabel}>Total Time</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{formatTime(averageWeeklyTime)}</Text>
            <Text style={styles.summaryLabel}>Avg/Week</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{totalSessions}</Text>
            <Text style={styles.summaryLabel}>Total Sessions</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{weeksWithTarget}</Text>
            <Text style={styles.summaryLabel}>Goals Met</Text>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <MaterialIcons name="view-week" size={48} color="#6366F1" />
        <Text style={styles.loadingText}>Loading weekly analytics...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialIcons name="error-outline" size={48} color="#EF4444" />
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {renderWeeklySummary()}
      {renderWeeklyChart()}
      {renderWeeklyList()}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryContainer: {
    margin: 20,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    minWidth: (width - 52) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    color: '#64748B',
  },
  chartContainer: {
    backgroundColor: '#FFFFFF',
    margin: 20,
    marginTop: 0,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  barChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
    paddingBottom: 40,
  },
  barContainer: {
    alignItems: 'center',
    minWidth: 50,
  },
  barWrapper: {
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  bar: {
    width: 32,
    borderRadius: 16,
    minHeight: 4,
  },
  barLabel: {
    fontSize: 10,
    color: '#64748B',
    marginBottom: 2,
    textAlign: 'center',
  },
  barLabelCurrent: {
    fontWeight: '600',
    color: '#1E293B',
  },
  barValue: {
    fontSize: 9,
    color: '#64748B',
    textAlign: 'center',
  },
  listContainer: {
    margin: 20,
    marginTop: 0,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  weeklyItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  weeklyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  weeklyDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
  },
  weeklyDate: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1E293B',
  },
  weeklyDateCurrent: {
    color: '#6366F1',
    fontWeight: '600',
  },
  weeklyDuration: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6366F1',
  },
  weeklyDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 12,
  },
  weeklyDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  weeklyDetailText: {
    fontSize: 12,
    color: '#64748B',
  },
  progressContainer: {
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
    paddingTop: 12,
  },
  progressBar: {
    height: 6,
    backgroundColor: '#E2E8F0',
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: '#64748B',
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#64748B',
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    marginTop: 12,
    textAlign: 'center',
  },
});
