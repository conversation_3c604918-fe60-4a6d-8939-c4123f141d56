import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { Analytics, ChartDataPoint, TimeSeriesDataPoint } from '@/types/app';

const { width } = Dimensions.get('window');

interface OverviewTabProps {
  analytics: Analytics | null;
  timeSeriesData: TimeSeriesDataPoint[];
  subjectChartData: ChartDataPoint[];
  taskTypeChartData: ChartDataPoint[];
  performanceMetrics: any;
  quickStats: any;
  todayProgress: any;
  weekProgress: any;
  formatTime: (seconds: number) => string;
  getStreakMessage: () => string;
  loading: boolean;
  error: string | null;
}

export function OverviewTab({
  analytics,
  quickStats,
  todayProgress,
  weekProgress,
  formatTime,
  getStreakMessage,
  loading,
  error,
}: OverviewTabProps) {
  
  const renderQuickStats = () => {
    if (!quickStats) return null;

    return (
      <View style={styles.statsGrid}>
        <View style={styles.statCard}>
          <MaterialIcons name="today" size={24} color="#6366F1" />
          <Text style={styles.statValue}>{formatTime(quickStats.todayTime)}</Text>
          <Text style={styles.statLabel}>Today</Text>
        </View>

        <View style={styles.statCard}>
          <MaterialIcons name="view-week" size={24} color="#8B5CF6" />
          <Text style={styles.statValue}>{formatTime(quickStats.weekTime)}</Text>
          <Text style={styles.statLabel}>This Week</Text>
        </View>

        <View style={styles.statCard}>
          <MaterialIcons name="calendar-month" size={24} color="#EC4899" />
          <Text style={styles.statValue}>{formatTime(quickStats.monthTime)}</Text>
          <Text style={styles.statLabel}>This Month</Text>
        </View>

        <View style={styles.statCard}>
          <MaterialIcons name="local-fire-department" size={24} color="#F59E0B" />
          <Text style={styles.statValue}>{quickStats.currentStreak}</Text>
          <Text style={styles.statLabel}>Day Streak</Text>
        </View>
      </View>
    );
  };

  const renderProgressCards = () => {
    return (
      <View style={styles.progressSection}>
        {/* Today's Progress */}
        {todayProgress && (
          <View style={styles.progressCard}>
            <View style={styles.progressHeader}>
              <Text style={styles.progressTitle}>Today's Goal</Text>
              <Text style={styles.progressPercentage}>
                {Math.round(todayProgress.percentage)}%
              </Text>
            </View>
            <View style={styles.progressBarContainer}>
              <View style={styles.progressBarBackground}>
                <LinearGradient
                  colors={['#6366F1', '#8B5CF6']}
                  style={[
                    styles.progressBarFill,
                    { width: `${Math.min(todayProgress.percentage, 100)}%` }
                  ]}
                />
              </View>
            </View>
            <Text style={styles.progressText}>
              {todayProgress.current} / {todayProgress.target} minutes
            </Text>
          </View>
        )}

        {/* Week's Progress */}
        {weekProgress && (
          <View style={styles.progressCard}>
            <View style={styles.progressHeader}>
              <Text style={styles.progressTitle}>Weekly Goal</Text>
              <Text style={styles.progressPercentage}>
                {Math.round(weekProgress.percentage)}%
              </Text>
            </View>
            <View style={styles.progressBarContainer}>
              <View style={styles.progressBarBackground}>
                <LinearGradient
                  colors={['#10B981', '#059669']}
                  style={[
                    styles.progressBarFill,
                    { width: `${Math.min(weekProgress.percentage, 100)}%` }
                  ]}
                />
              </View>
            </View>
            <Text style={styles.progressText}>
              {weekProgress.current} / {weekProgress.target} minutes
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderStreakCard = () => {
    const streakMessage = getStreakMessage();
    if (!streakMessage) return null;

    return (
      <LinearGradient
        colors={['#F59E0B', '#D97706']}
        style={styles.streakCard}
      >
        <MaterialIcons name="local-fire-department" size={32} color="#FFFFFF" />
        <View style={styles.streakContent}>
          <Text style={styles.streakTitle}>Study Streak</Text>
          <Text style={styles.streakMessage}>{streakMessage}</Text>
        </View>
      </LinearGradient>
    );
  };

  const renderInsights = () => {
    if (!analytics) return null;

    const insights = [];

    // Total sessions insight
    if (quickStats?.totalSessions > 0) {
      insights.push({
        icon: 'analytics',
        title: 'Study Sessions',
        text: `You've completed ${quickStats.totalSessions} study sessions. Keep up the consistency!`,
        color: '#6366F1'
      });
    }

    // Top subject insight
    if (analytics.subjectStats.length > 0) {
      const topSubject = analytics.subjectStats[0];
      insights.push({
        icon: 'school',
        title: 'Top Subject',
        text: `${topSubject.subject} is your most studied subject with ${formatTime(topSubject.totalDuration)}.`,
        color: topSubject.color
      });
    }

    // Streak insight
    if (analytics.streakInfo.currentStreak > 0) {
      insights.push({
        icon: 'local-fire-department',
        title: 'Consistency',
        text: `You're on a ${analytics.streakInfo.currentStreak}-day study streak. Amazing dedication!`,
        color: '#F59E0B'
      });
    }

    return (
      <View style={styles.insightsSection}>
        <Text style={styles.sectionTitle}>Insights</Text>
        {insights.map((insight, index) => (
          <View key={index} style={styles.insightCard}>
            <View style={[styles.insightIcon, { backgroundColor: `${insight.color}20` }]}>
              <MaterialIcons name={insight.icon as any} size={20} color={insight.color} />
            </View>
            <View style={styles.insightContent}>
              <Text style={styles.insightTitle}>{insight.title}</Text>
              <Text style={styles.insightText}>{insight.text}</Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <MaterialIcons name="analytics" size={48} color="#6366F1" />
        <Text style={styles.loadingText}>Loading analytics...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialIcons name="error-outline" size={48} color="#EF4444" />
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {renderQuickStats()}
      {renderProgressCards()}
      {renderStreakCard()}
      {renderInsights()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 20,
  },
  statCard: {
    flex: 1,
    minWidth: (width - 52) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1E293B',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#64748B',
    textAlign: 'center',
  },
  progressSection: {
    gap: 16,
    marginBottom: 20,
  },
  progressCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
  },
  progressPercentage: {
    fontSize: 16,
    fontWeight: '700',
    color: '#6366F1',
  },
  progressBarContainer: {
    marginBottom: 8,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: '#E2E8F0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: '#64748B',
  },
  streakCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
  },
  streakContent: {
    marginLeft: 16,
    flex: 1,
  },
  streakTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  streakMessage: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
  },
  insightsSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 16,
  },
  insightCard: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  insightIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  insightContent: {
    flex: 1,
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 4,
  },
  insightText: {
    fontSize: 13,
    color: '#64748B',
    lineHeight: 18,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#64748B',
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    marginTop: 12,
    textAlign: 'center',
  },
});
